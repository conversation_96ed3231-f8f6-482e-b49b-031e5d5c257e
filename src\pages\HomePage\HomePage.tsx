import React from 'react';
import { Hero } from '../../components/Hero/Hero';
import { Skills } from '../../components/Skills/Skills';
import { Projects } from '../../components/Projects/Projects';

const HomePage: React.FC = () => {
  return (
    <div className="home-page">
      <Hero />
      <Skills />
      <Projects />

      <section id="contact" className="section">
        <div className="section__container">
          <div className="section__header">
            <h2 className="section__title section__title--gradient">Get In Touch</h2>
            <p className="section__subtitle">
              Let's work together on something amazing
            </p>
          </div>
          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <a
              href="mailto:<EMAIL>"
              className="btn btn--primary btn--large"
              style={{ marginRight: '1rem' }}
            >
              Send Email
            </a>
            <a
              href="https://linkedin.com/in/nishant"
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn--secondary btn--large"
            >
              LinkedIn
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
