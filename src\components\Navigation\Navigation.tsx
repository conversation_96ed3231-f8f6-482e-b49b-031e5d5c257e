import { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from '../../hooks/useTheme';

export const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();
  const navRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsScrolled(scrollY > 50);

      // Update active section based on scroll position
      const sections = ['home', 'skills', 'projects', 'contact'];
      const sectionElements = sections.map(id => document.getElementById(id) || document.querySelector(`.${id}`));

      let currentSection = 'home';
      sectionElements.forEach((section, index) => {
        if (section) {
          const rect = section.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = sections[index];
          }
        }
      });

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [location]);

  // Removed complex animations for now

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    const target = document.getElementById(targetId) || document.querySelector(`.${targetId}`);

    if (target) {
      const targetPosition = target.offsetTop - 80;
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }

    setIsMenuOpen(false);
  };

  const navLinks = [
    { href: '#home', label: 'Home', id: 'home' },
    { href: '/about', label: 'About', id: 'about' },
    { href: '#skills', label: 'Skills', id: 'skills' },
    { href: '#projects', label: 'Projects', id: 'projects' },
    { href: '#contact', label: 'Contact', id: 'contact' }
  ];

  return (
    <nav ref={navRef} className={`navigation ${isScrolled ? 'navigation--scrolled' : ''}`}>
      <div className="navigation__container">
        <Link to="/" className="navigation__logo">
          <span className="navigation__logo-text">NK</span>
          <div className="navigation__logo-glow" />
        </Link>

        <div className={`navigation__menu ${isMenuOpen ? 'navigation__menu--open' : ''}`}>
          {navLinks.map((link) => (
            link.href.startsWith('#') ? (
              <a
                key={link.id}
                href={link.href}
                className={`navigation__link ${activeSection === link.id ? 'navigation__link--active' : ''}`}
                onClick={(e) => handleSmoothScroll(e, link.id)}
              >
                {link.label}
                <span className="navigation__link-glow" />
              </a>
            ) : (
              <Link
                key={link.id}
                to={link.href}
                className={`navigation__link ${location.pathname === link.href ? 'navigation__link--active' : ''}`}
              >
                {link.label}
                <span className="navigation__link-glow" />
              </Link>
            )
          ))}
        </div>

        <div className="navigation__actions">
          <button
            onClick={toggleTheme}
            className="navigation__theme-toggle"
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
          >
            <span className="navigation__theme-icon">
              {theme === 'light' ? '🌙' : '☀️'}
            </span>
            <div className="navigation__theme-glow" />
          </button>

          <button
            onClick={toggleMenu}
            className={`navigation__burger ${isMenuOpen ? 'navigation__burger--open' : ''}`}
            aria-label="Toggle menu"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div
          className="navigation__overlay"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </nav>
  );
};
