import React from 'react';
import { useParams } from 'react-router-dom';
import { projects } from '../../data/portfolio';
import type { Project } from '../../data/portfolio';

const ProjectPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const project = projects.find((p: Project) => p.slug === slug);

  if (!project) {
    return (
      <div className="project-page">
        <section className="section">
          <div className="section__container">
            <h1>Project not found</h1>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="project-page">
      <section className="section">
        <div className="section__container">
          <div className="section__header">
            <h1 className="section__title section__title--gradient">{project.title}</h1>
            <p className="section__subtitle">{project.subtitle}</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProjectPage;
