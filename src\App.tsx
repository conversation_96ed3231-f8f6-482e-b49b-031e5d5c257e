import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './components/ThemeProvider';
import { Navigation } from './components/Navigation/Navigation';
import { LoadingScreen } from './components/LoadingScreen/LoadingScreen';
import { ScrollToTop } from './components/ScrollToTop/ScrollToTop';

// Lazy load pages for better performance
const HomePage = lazy(() => import('./pages/HomePage/HomePage'));
const ProjectPage = lazy(() => import('./pages/ProjectPage/ProjectPage'));
const AboutPage = lazy(() => import('./pages/AboutPage/AboutPage'));

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="app">
          <ScrollToTop />
          <Navigation />
          <main className="main-content">
            <Suspense fallback={<LoadingScreen />}>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/about" element={<AboutPage />} />
                <Route path="/projects/:slug" element={<ProjectPage />} />
                <Route path="*" element={<HomePage />} />
              </Routes>
            </Suspense>
          </main>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
