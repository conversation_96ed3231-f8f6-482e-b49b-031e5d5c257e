import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { projects } from '../../data/portfolio';
import type { Project } from '../../data/portfolio';

interface ProjectCardProps {
  project: Project;
  index: number;
  isExpanded: boolean;
  onToggle: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, isExpanded, onToggle }) => {

  return (
    <div
      className={`project-card ${isExpanded ? 'project-card--expanded' : ''} ${project.featured ? 'project-card--featured' : ''}`}
      onClick={onToggle}
      data-project-id={project.id}
    >
      <div className="project-card__image-container">
        <img
          src={`https://picsum.photos/600/400?random=${project.id}`}
          alt={project.title}
          className="project-card__image"
          loading="lazy"
        />
        <div className="project-card__overlay">
          <div className="project-card__overlay-content">
            <span className="project-card__view-text">View Project</span>
            <div className="project-card__arrow">→</div>
          </div>
        </div>
        
        <div className="project-card__category">
          {project.category}
        </div>
      </div>
      
      <div className="project-card__content">
        <div className="project-card__header">
          <h3 className="project-card__title">{project.title}</h3>
          <p className="project-card__subtitle">{project.subtitle}</p>
        </div>
        
        <p className="project-card__description">
          {isExpanded ? project.longDescription : project.description}
        </p>
        
        <div className="project-card__tech-stack">
          {project.technologies.slice(0, isExpanded ? project.technologies.length : 3).map((tech: string) => (
            <span key={tech} className="project-card__tech-tag">
              {tech}
            </span>
          ))}
          {!isExpanded && project.technologies.length > 3 && (
            <span className="project-card__tech-tag project-card__tech-tag--more">
              +{project.technologies.length - 3}
            </span>
          )}
        </div>
        
        <div className="project-card__actions">
          {project.demoUrl && (
            <a 
              href={project.demoUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="project-card__link"
              onClick={(e) => e.stopPropagation()}
            >
              <span>Live Demo</span>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6M15 3h6v6M10 14L21 3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </a>
          )}
          
          {project.githubUrl && (
            <a 
              href={project.githubUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="project-card__link"
              onClick={(e) => e.stopPropagation()}
            >
              <span>GitHub</span>
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
              </svg>
            </a>
          )}
          
          <Link 
            to={`/projects/${project.slug}`}
            className="project-card__link project-card__link--primary"
            onClick={(e) => e.stopPropagation()}
          >
            <span>Learn More</span>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M5 12h14M12 5l7 7-7 7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
        </div>
      </div>
      
      <div className="project-card__glow" />
    </div>
  );
};

export const Projects: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [expandedProject, setExpandedProject] = useState<string | null>(null);

  const featuredProjects = projects.filter((p: Project) => p.featured);
  const otherProjects = projects.filter((p: Project) => !p.featured);

  // Removed complex animations for now

  const handleProjectToggle = (projectId: string) => {
    setExpandedProject(expandedProject === projectId ? null : projectId);
  };

  return (
    <section ref={sectionRef} id="projects" className="section projects-section">
      <div className="section__container">
        <div className="section__header">
          <h2 className="section__title section__title--gradient">
            Featured Projects
          </h2>
          <p className="section__subtitle">
            A showcase of my recent work and creative solutions
          </p>
        </div>

        {/* Featured Projects */}
        <div className="projects-featured">
          {featuredProjects.map((project: Project, index: number) => (
            <ProjectCard
              key={project.id}
              project={project}
              index={index}
              isExpanded={expandedProject === project.id}
              onToggle={() => handleProjectToggle(project.id)}
            />
          ))}
        </div>

        {/* Other Projects Grid */}
        {otherProjects.length > 0 && (
          <>
            <div className="projects-divider">
              <h3 className="projects-divider__title">More Projects</h3>
              <div className="projects-divider__line" />
            </div>
            
            <div className="projects-grid">
              {otherProjects.map((project: Project, index: number) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  index={index + featuredProjects.length}
                  isExpanded={expandedProject === project.id}
                  onToggle={() => handleProjectToggle(project.id)}
                />
              ))}
            </div>
          </>
        )}

        {/* Background Elements */}
        <div className="projects-background">
          <div className="projects-background__mesh">
            {Array.from({ length: 50 }, (_, i) => (
              <div key={i} className="projects-background__dot" />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
