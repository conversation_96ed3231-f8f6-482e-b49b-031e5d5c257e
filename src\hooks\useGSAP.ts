import { useRef, useLayoutEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
  fadeIn,
  textReveal,
  morphingBlob,
  particleSystem,
  magneticHover,
  killAnimations,
  conditionalAnimation,
  ANIMATION_PRESETS
} from '../animations/gsapUtils';

// ===== CORE GSAP HOOK =====
export const useGSAP = (
  callback: (context: { timeline: gsap.core.Timeline; selector: gsap.utils.SelectorFunc }) => void,
  dependencies: any[] = []
) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      const timeline = gsap.timeline();
      const selector = gsap.utils.selector(containerRef.current);
      callback({ timeline, selector });
    }, containerRef);

    return () => ctx.revert();
  }, dependencies);

  return containerRef;
};

// ===== FADE IN HOOK =====
export const useFadeIn = (
  options: {
    direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'none';
    duration?: number;
    delay?: number;
    distance?: number;
    ease?: string;
    stagger?: number;
    trigger?: boolean;
  } = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const animationRef = useRef<gsap.core.Tween | null>(null);

  useLayoutEffect(() => {
    if (!elementRef.current) return;

    const animation = conditionalAnimation(
      elementRef.current,
      () => fadeIn(elementRef.current!, options)
    );

    animationRef.current = animation;

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, []);

  return elementRef;
};

// ===== TEXT REVEAL HOOK =====
export const useTextReveal = (
  options: {
    type?: 'words' | 'chars' | 'lines';
    duration?: number;
    stagger?: number;
    ease?: string;
    delay?: number;
    trigger?: boolean;
  } = {}
) => {
  const textRef = useRef<HTMLElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useLayoutEffect(() => {
    if (!textRef.current) return;

    const timeline = conditionalAnimation(
      textRef.current,
      () => textReveal(textRef.current!, options)
    );

    timelineRef.current = timeline;

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, []);

  const replay = () => {
    if (timelineRef.current) {
      timelineRef.current.restart();
    }
  };

  return { ref: textRef, replay };
};

// ===== SCROLL REVEAL HOOK =====
export const useScrollReveal = (
  options: {
    direction?: 'up' | 'down' | 'left' | 'right' | 'scale';
    duration?: number;
    distance?: number;
    start?: string;
    end?: string;
    scrub?: boolean | number;
    stagger?: number;
  } = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const scrollTriggerRef = useRef<ScrollTrigger | null>(null);

  useLayoutEffect(() => {
    if (!elementRef.current) return;

    const {
      direction = 'up',
      duration = ANIMATION_PRESETS.duration.normal,
      distance = 50,
      start = 'top 80%',
      end = 'bottom 20%',
      scrub = false,
      stagger = 0
    } = options;

    const animation = fadeIn(elementRef.current, {
      direction,
      duration,
      distance,
      stagger
    });

    scrollTriggerRef.current = ScrollTrigger.create({
      trigger: elementRef.current,
      start,
      end,
      scrub,
      animation,
      toggleActions: 'play none none reverse'
    });

    return () => {
      if (scrollTriggerRef.current) {
        scrollTriggerRef.current.kill();
      }
    };
  }, []);

  return elementRef;
};

// ===== MAGNETIC HOVER HOOK =====
export const useMagneticHover = (
  options: {
    strength?: number;
    speed?: number;
  } = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  useLayoutEffect(() => {
    if (!elementRef.current) return;

    cleanupRef.current = magneticHover(elementRef.current, options);

    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  return elementRef;
};

// ===== MORPHING BLOB HOOK =====
export const useMorphingBlob = (
  options: {
    duration?: number;
    ease?: string;
    repeat?: number;
    yoyo?: boolean;
  } = {}
) => {
  const blobRef = useRef<SVGElement>(null);
  const animationRef = useRef<gsap.core.Tween | null>(null);

  useLayoutEffect(() => {
    if (!blobRef.current) return;

    const animation = conditionalAnimation(
      blobRef.current,
      () => morphingBlob(blobRef.current!, options)
    );

    animationRef.current = animation;

    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
    };
  }, []);

  return blobRef;
};

// ===== PARTICLE SYSTEM HOOK =====
export const useParticleSystem = (
  options: {
    count?: number;
    colors?: string[];
    size?: { min: number; max: number };
    speed?: { min: number; max: number };
    life?: { min: number; max: number };
    autoStart?: boolean;
  } = {}
) => {
  const containerRef = useRef<HTMLElement>(null);
  const particlesRef = useRef<HTMLElement[]>([]);

  useLayoutEffect(() => {
    if (!containerRef.current || !options.autoStart) return;

    const particles = conditionalAnimation(
      containerRef.current,
      () => particleSystem(containerRef.current!, options)
    );

    if (particles) {
      particlesRef.current = particles;
    }

    return () => {
      particlesRef.current.forEach(particle => {
        killAnimations(particle);
        particle.remove();
      });
      particlesRef.current = [];
    };
  }, [options.autoStart]);

  const startParticles = () => {
    if (containerRef.current && particlesRef.current.length === 0) {
      const particles = particleSystem(containerRef.current, options);
      if (particles) {
        particlesRef.current = particles;
      }
    }
  };

  const stopParticles = () => {
    particlesRef.current.forEach(particle => {
      killAnimations(particle);
      particle.remove();
    });
    particlesRef.current = [];
  };

  return { ref: containerRef, startParticles, stopParticles };
};

// ===== TIMELINE HOOK =====
export const useTimeline = (
  callback: (timeline: gsap.core.Timeline) => void,
  dependencies: any[] = []
) => {
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useLayoutEffect(() => {
    timelineRef.current = gsap.timeline({ paused: true });
    callback(timelineRef.current);

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, dependencies);

  const play = () => timelineRef.current?.play();
  const pause = () => timelineRef.current?.pause();
  const reverse = () => timelineRef.current?.reverse();
  const restart = () => timelineRef.current?.restart();
  const seek = (time: number) => timelineRef.current?.seek(time);

  return { play, pause, reverse, restart, seek, timeline: timelineRef.current };
};

// ===== HOVER ANIMATION HOOK =====
export const useHoverAnimation = (
  enterAnimation: (element: HTMLElement) => gsap.core.Tween | gsap.core.Timeline,
  leaveAnimation: (element: HTMLElement) => gsap.core.Tween | gsap.core.Timeline
) => {
  const elementRef = useRef<HTMLElement>(null);

  useLayoutEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    let enterTween: gsap.core.Tween | gsap.core.Timeline | null = null;
    let leaveTween: gsap.core.Tween | gsap.core.Timeline | null = null;

    const handleMouseEnter = () => {
      if (leaveTween) leaveTween.kill();
      enterTween = enterAnimation(element);
    };

    const handleMouseLeave = () => {
      if (enterTween) enterTween.kill();
      leaveTween = leaveAnimation(element);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      if (enterTween) enterTween.kill();
      if (leaveTween) leaveTween.kill();
    };
  }, []);

  return elementRef;
};

// ===== INTERSECTION OBSERVER HOOK =====
export const useIntersectionAnimation = (
  animation: (element: HTMLElement) => gsap.core.Tween | gsap.core.Timeline,
  options: IntersectionObserverInit = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const hasAnimated = useRef(false);

  useLayoutEffect(() => {
    if (!elementRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated.current) {
            animation(entry.target as HTMLElement);
            hasAnimated.current = true;
          }
        });
      },
      { threshold: 0.1, ...options }
    );

    observerRef.current.observe(elementRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return elementRef;
};
