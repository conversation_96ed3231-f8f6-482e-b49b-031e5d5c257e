import { useRef } from 'react';
import { skills } from '../../data/portfolio';
import type { Skill } from '../../data/portfolio';

interface SkillCardProps {
  skill: Skill;
  index: number;
}

const SkillCard: React.FC<SkillCardProps> = ({ skill }) => {

  const renderSkillLevel = (level: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <div
        key={i}
        className={`skill-card__dot ${i < level ? 'skill-card__dot--active' : ''}`}
        style={{ animationDelay: `${(index * 0.1) + (i * 0.05)}s` }}
      />
    ));
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      frontend: '🎨',
      backend: '⚙️',
      tools: '🛠️',
      design: '✨'
    };
    return icons[category as keyof typeof icons] || '💻';
  };

  return (
    <div
      className={`skill-card skill-card--${skill.category}`}
      data-skill-level={skill.level}
    >
      <div className="skill-card__icon">
        {getCategoryIcon(skill.category)}
      </div>
      
      <div className="skill-card__content">
        <h3 className="skill-card__name">{skill.name}</h3>
        <div className="skill-card__level">
          {renderSkillLevel(skill.level)}
        </div>
      </div>
      
      <div className="skill-card__glow" />
      
      <div className="skill-card__particles">
        {Array.from({ length: 3 }, (_, i) => (
          <div key={i} className="skill-card__particle" />
        ))}
      </div>
    </div>
  );
};

export const Skills: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);

  // Group skills by category
  const skillsByCategory = skills.reduce((acc: Record<string, Skill[]>, skill: Skill) => {
    if (!acc[skill.category]) {
      acc[skill.category] = [];
    }
    acc[skill.category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  const categoryTitles = {
    frontend: 'Frontend Development',
    backend: 'Backend Development', 
    tools: 'Tools & Technologies',
    design: 'Design & UI/UX'
  };

  // Removed complex animations for now

  return (
    <section ref={sectionRef} id="skills" className="section skills-section">
      <div className="section__container">
        <div className="section__header">
          <h2 className="section__title section__title--gradient">
            Skills & Expertise
          </h2>
          <p className="section__subtitle">
            Technologies I work with to bring ideas to life
          </p>
        </div>

        <div className="skills-grid">
          {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
            <div key={category} className="skill-category">
              <div className="skill-category__header">
                <div className="skill-category__icon">
                  {category === 'frontend' && '🎨'}
                  {category === 'backend' && '⚙️'}
                  {category === 'tools' && '🛠️'}
                  {category === 'design' && '✨'}
                </div>
                <h3 className="skill-category__title">
                  {categoryTitles[category as keyof typeof categoryTitles]}
                </h3>
              </div>
              
              <div className="skill-category__skills">
                {(categorySkills as Skill[]).map((skill: Skill, index: number) => (
                  <SkillCard 
                    key={skill.name} 
                    skill={skill} 
                    index={index}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Interactive Background Elements */}
        <div className="skills-background">
          <div className="skills-background__grid">
            {Array.from({ length: 20 }, (_, i) => (
              <div key={i} className="skills-background__dot" />
            ))}
          </div>
          
          <div className="skills-background__lines">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="skills-background__line" />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
