// ===== EXTRAVAGANT HOME PAGE STYLES =====
.home-page {
  position: relative;
  overflow-x: hidden;
  background: #0a0a0a;
}

// ===== FLOWFEST-INSPIRED HERO SECTION =====
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);

  // Continuous image strip
  &__image-strip {
    position: absolute;
    top: 20%;
    left: 0;
    width: 100%;
    height: 120px;
    overflow: hidden;
    z-index: 1;
    opacity: 0.3;
  }

  &__image-track {
    display: flex;
    width: 200%;
    height: 100%;
    animation: infiniteScroll 20s linear infinite;
  }

  &__image-item {
    flex: 0 0 200px;
    height: 100%;
    margin-right: 20px;
  }

  &__image-placeholder {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  // Floating elements
  &__floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
  }

  &__floating-element {
    position: absolute;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.6);
    animation: float 6s ease-in-out infinite;

    &--1 {
      top: 15%;
      left: 10%;
      animation-delay: 0s;
    }

    &--2 {
      top: 25%;
      right: 15%;
      animation-delay: 1s;
    }

    &--3 {
      bottom: 30%;
      left: 20%;
      animation-delay: 2s;
    }

    &--4 {
      top: 60%;
      right: 25%;
      animation-delay: 3s;
    }

    &--5 {
      bottom: 15%;
      right: 10%;
      animation-delay: 4s;
    }
  }

  // Main container
  &__container {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
  }

  // Content wrapper
  &__content {
    color: white;
    position: relative;
  }

  // Title wrapper
  &__title-wrapper {
    margin-bottom: 2rem;
  }

  // Main title
  &__title-main {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    margin-bottom: 1rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  // Dynamic title
  &__title-dynamic {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  &__title-prefix {
    opacity: 0.8;
  }

  &__title-word {
    background: linear-gradient(45deg, #feca57, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(45deg, #feca57, #ff6b6b);
      border-radius: 2px;
      animation: underlineGrow 0.6s ease-out;
    }
  }

  // Subtitle
  &__subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    font-weight: 400;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.6;
  }

  // CTA buttons
  &__cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;

    .btn {
      position: relative;
      padding: 1rem 2rem;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;

      &--primary {
        background: white;
        color: #ff6b6b;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
        }
      }

      &--secondary {
        background: transparent;
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: white;
          transform: translateY(-3px);
        }
      }

      &__bg {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:hover &__bg {
        left: 100%;
      }

      span {
        position: relative;
        z-index: 2;
      }
    }
  }

  // Status badge
  &__status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 4rem;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ecdc4;
    animation: pulse 2s infinite;
  }

  // Scroll indicator
  &__scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      color: white;
      transform: translateX(-50%) translateY(-5px);
    }

    span {
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.1em;
    }
  }

  &__scroll-arrow {
    font-size: 1.2rem;
    animation: bounce 2s infinite;
  }

  // Background gradient
  &__gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(255, 107, 107, 0.1) 0%,
      rgba(78, 205, 196, 0.1) 50%,
      rgba(69, 183, 209, 0.1) 100%
    );
    z-index: 1;
  }
}

// Keyframe animations
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes underline-expand {
  to { transform: scaleX(1); }
}

// Particle styles
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  filter: blur(1px);

  &--primary {
    background: var(--accent-primary);
    box-shadow: 0 0 10px var(--accent-primary);
  }

  &--secondary {
    background: var(--accent-secondary);
    box-shadow: 0 0 10px var(--accent-secondary);
  }

  &--tertiary {
    background: var(--accent-tertiary);
    box-shadow: 0 0 10px var(--accent-tertiary);
  }
}

// ===== ABOUT SECTION =====
.about-section {
  .about-content {
    @include grid-center;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-16;
    align-items: center;
    
    @include tablet-up {
      grid-template-columns: 1fr;
      gap: $spacing-12;
      text-align: center;
    }
  }
  
  .about-text {
    &__intro {
      @include body-large;
      color: var(--text-secondary);
      margin-bottom: $spacing-6;
      line-height: $line-height-relaxed;
    }
    
    &__highlight {
      color: var(--accent-primary);
      font-weight: $font-weight-semibold;
    }
    
    &__stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-6;
      margin-top: $spacing-8;
      
      @include mobile-only {
        grid-template-columns: 1fr;
        gap: $spacing-4;
      }
    }
  }
  
  .stat-item {
    text-align: center;
    
    &__number {
      @include heading-3;
      color: var(--accent-primary);
      margin-bottom: $spacing-2;
    }
    
    &__label {
      @include body-small;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .about-visual {
    position: relative;
    
    &__image {
      width: 100%;
      max-width: 400px;
      height: 500px;
      object-fit: cover;
      border-radius: $border-radius-3xl;
      box-shadow: var(--card-hover-shadow);
    }
    
    &__decoration {
      position: absolute;
      top: -20px;
      right: -20px;
      width: 100px;
      height: 100px;
      background: var(--gradient-primary);
      border-radius: 50%;
      opacity: 0.8;
      filter: blur(20px);
    }
  }
}

// ===== SKILLS SECTION =====
.skills-section {
  position: relative;
  background: var(--bg-secondary);

  .skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: $spacing-12;
    margin-top: $spacing-16;
    position: relative;
    z-index: 2;

    @include mobile-only {
      grid-template-columns: 1fr;
      gap: $spacing-8;
      margin-top: $spacing-12;
    }
  }

  .skill-category {
    position: relative;

    &__header {
      @include flex-center;
      gap: $spacing-4;
      margin-bottom: $spacing-8;
      text-align: center;
    }

    &__icon {
      font-size: $font-size-4xl;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      animation: float 3s ease-in-out infinite;
    }

    &__title {
      @include heading-4;
      color: var(--text-primary);
      background: linear-gradient(135deg,
        var(--accent-primary),
        var(--accent-secondary));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &__skills {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: $spacing-4;

      @include mobile-only {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: $spacing-3;
      }
    }
  }
}

// ===== SKILL CARD =====
.skill-card {
  position: relative;
  padding: $spacing-6;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: $border-radius-2xl;
  text-align: center;
  cursor: pointer;
  overflow: hidden;
  @include smooth-transition((transform, box-shadow, border-color));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      var(--accent-primary) 0%,
      var(--accent-secondary) 100%);
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 30px rgba(255, 107, 107, 0.3);
    border-color: var(--accent-primary);

    &::before {
      opacity: 0.05;
    }

    .skill-card__icon {
      transform: scale(1.2) rotate(10deg);
    }

    .skill-card__glow {
      opacity: 1;
      transform: scale(1.5);
    }

    .skill-card__particles .skill-card__particle {
      animation-play-state: running;
    }
  }

  &__icon {
    font-size: $font-size-3xl;
    margin-bottom: $spacing-4;
    @include smooth-transition((transform, filter));
    position: relative;
    z-index: 3;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  &__content {
    position: relative;
    z-index: 3;
  }

  &__name {
    @include body-base;
    font-weight: $font-weight-semibold;
    color: var(--text-primary);
    margin-bottom: $spacing-3;
    @include smooth-transition(color);
  }

  &__level {
    @include flex-center;
    gap: $spacing-1;
    justify-content: center;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-primary);
    @include smooth-transition((background-color, transform));
    animation: skill-dot-pulse 2s ease-in-out infinite;
    animation-play-state: paused;

    &--active {
      background: var(--accent-primary);
      box-shadow: 0 0 8px var(--accent-primary);
      animation-play-state: running;
    }
  }

  &__glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle,
      var(--accent-primary) 0%,
      transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    @include smooth-transition((transform, opacity));
    z-index: 2;
    filter: blur(20px);
  }

  &__particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 2;
  }

  &__particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-primary);
    border-radius: 50%;
    opacity: 0;
    animation: skill-particle-float 3s ease-in-out infinite;
    animation-play-state: paused;

    &:nth-child(1) {
      top: 20%;
      left: 20%;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      top: 30%;
      right: 25%;
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      bottom: 25%;
      left: 30%;
      animation-delay: 1s;
    }
  }

  // Category-specific styling
  &--frontend {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(255, 107, 107, 0.4);
    }
  }

  &--backend {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(78, 205, 196, 0.4);
    }
  }

  &--tools {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(69, 183, 209, 0.4);
    }
  }

  &--design {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(254, 202, 87, 0.4);
    }
  }

  @include mobile-only {
    padding: $spacing-4;

    &__icon {
      font-size: $font-size-2xl;
      margin-bottom: $spacing-3;
    }

    &__name {
      font-size: $font-size-sm;
    }

    &__dot {
      width: 6px;
      height: 6px;
    }
  }
}

// ===== SKILLS BACKGROUND =====
.skills-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  &__grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 2px;
    opacity: 0.1;
  }

  &__dot {
    width: 2px;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: grid-dot-pulse 4s ease-in-out infinite;
    animation-delay: calc(var(--i) * 0.1s);
  }

  &__lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &__line {
    position: absolute;
    background: linear-gradient(90deg,
      transparent,
      var(--accent-primary),
      transparent);
    height: 1px;
    width: 100%;
    opacity: 0.1;
    animation: line-move 8s ease-in-out infinite;

    &:nth-child(1) { top: 20%; animation-delay: 0s; }
    &:nth-child(2) { top: 40%; animation-delay: 1s; }
    &:nth-child(3) { top: 60%; animation-delay: 2s; }
    &:nth-child(4) { top: 80%; animation-delay: 3s; }
    &:nth-child(5) { top: 100%; animation-delay: 4s; }
  }
}

// ===== KEYFRAME ANIMATIONS =====
@keyframes skill-dot-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

@keyframes skill-particle-float {
  0% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-20px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-40px) scale(0);
  }
}

@keyframes grid-dot-pulse {
  0%, 100% { opacity: 0.1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.5); }
}

@keyframes line-move {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

// ===== TIMELINE SECTION =====
.timeline-section {
  .timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding-left: $spacing-8;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--border-primary);
    }
    
    @include mobile-only {
      padding-left: $spacing-6;
    }
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: $spacing-12;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &::before {
      content: '';
      position: absolute;
      left: -$spacing-8;
      top: $spacing-6;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--accent-primary);
      border: 3px solid var(--bg-primary);
      
      @include mobile-only {
        left: -$spacing-6;
      }
    }
  }
}

// ===== PROJECTS SECTION =====
.projects-section {
  position: relative;
  background: var(--bg-primary);

  .projects-featured {
    display: grid;
    gap: $spacing-12;
    margin-top: $spacing-16;

    @include mobile-only {
      gap: $spacing-8;
      margin-top: $spacing-12;
    }
  }

  .projects-divider {
    @include flex-center;
    gap: $spacing-6;
    margin: $spacing-20 0 $spacing-16;

    &__title {
      @include heading-4;
      color: var(--text-secondary);
      white-space: nowrap;
    }

    &__line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg,
        transparent,
        var(--border-primary),
        transparent);
    }

    @include mobile-only {
      margin: $spacing-16 0 $spacing-12;
    }
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: $spacing-8;

    @include mobile-only {
      grid-template-columns: 1fr;
      gap: $spacing-6;
    }
  }
}

// ===== PROJECT CARD =====
.project-card {
  position: relative;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: $border-radius-2xl;
  overflow: hidden;
  cursor: pointer;
  @include smooth-transition((transform, box-shadow, border-color));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      var(--accent-primary) 0%,
      var(--accent-secondary) 100%);
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-12px);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 40px rgba(255, 107, 107, 0.2);
    border-color: var(--accent-primary);

    &::before {
      opacity: 0.03;
    }

    .project-card__image {
      transform: scale(1.05);
    }

    .project-card__overlay {
      opacity: 1;
    }

    .project-card__glow {
      opacity: 1;
      transform: scale(1.2);
    }

    .project-card__category {
      transform: translateY(-5px);
      background: var(--accent-primary);
      color: var(--btn-primary-text);
    }
  }

  &--featured {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;

    @include tablet-up {
      grid-template-columns: 1fr;
    }

    .project-card__image-container {
      order: 1;

      @include tablet-up {
        order: 0;
        height: 300px;
      }
    }

    .project-card__content {
      order: 0;
      padding: $spacing-8;
      @include flex-column;
      justify-content: center;

      @include tablet-up {
        order: 1;
        padding: $spacing-6;
      }
    }
  }

  &--expanded {
    .project-card__description {
      max-height: none;
      -webkit-line-clamp: unset;
    }

    .project-card__tech-stack {
      max-height: none;
      overflow: visible;
    }
  }

  &__image-container {
    position: relative;
    height: 250px;
    overflow: hidden;

    .project-card--featured & {
      height: 100%;
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    @include smooth-transition(transform);
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    @include flex-center;
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 2;
  }

  &__overlay-content {
    @include flex-center;
    @include flex-column;
    gap: $spacing-3;
    color: white;
    text-align: center;
  }

  &__view-text {
    @include body-large;
    font-weight: $font-weight-semibold;
  }

  &__arrow {
    font-size: $font-size-2xl;
    @include smooth-transition(transform);

    .project-card:hover & {
      transform: translateX(5px);
    }
  }

  &__category {
    position: absolute;
    top: $spacing-4;
    right: $spacing-4;
    padding: $spacing-2 $spacing-4;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: $border-radius-full;
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    @include smooth-transition((transform, background-color, color));
    z-index: 3;
  }

  &__content {
    padding: $spacing-6;
    position: relative;
    z-index: 2;
  }

  &__header {
    margin-bottom: $spacing-4;
  }

  &__title {
    @include heading-4;
    color: var(--text-primary);
    margin-bottom: $spacing-2;

    .project-card--featured & {
      @include heading-3;
      margin-bottom: $spacing-3;
    }
  }

  &__subtitle {
    @include body-base;
    color: var(--accent-primary);
    font-weight: $font-weight-semibold;
  }

  &__description {
    @include body-base;
    color: var(--text-secondary);
    line-height: $line-height-relaxed;
    margin-bottom: $spacing-6;
    max-height: 4.5em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    @include smooth-transition(max-height);

    .project-card--featured & {
      @include body-large;
      max-height: 6em;
      -webkit-line-clamp: 4;
    }
  }

  &__tech-stack {
    @include flex-center;
    flex-wrap: wrap;
    gap: $spacing-2;
    margin-bottom: $spacing-6;
    max-height: 3em;
    overflow: hidden;
    @include smooth-transition(max-height);
  }

  &__tech-tag {
    padding: $spacing-1 $spacing-3;
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
    border-radius: $border-radius-full;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    @include smooth-transition((background-color, color));

    &--more {
      background: var(--accent-primary);
      color: var(--btn-primary-text);
    }

    .project-card:hover & {
      background: var(--hover-bg);
      color: var(--text-primary);

      &--more {
        background: var(--accent-secondary);
      }
    }
  }

  &__actions {
    @include flex-center;
    gap: $spacing-3;
    flex-wrap: wrap;
  }

  &__link {
    @include flex-center;
    gap: $spacing-2;
    padding: $spacing-2 $spacing-4;
    background: var(--hover-bg);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: $border-radius-lg;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    @include smooth-transition((background-color, color, transform));

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: var(--active-bg);
      color: var(--text-primary);
      transform: translateY(-2px);
    }

    &--primary {
      background: var(--accent-primary);
      color: var(--btn-primary-text);

      &:hover {
        background: var(--accent-secondary);
        color: var(--btn-primary-text);
      }
    }
  }

  &__glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle,
      var(--accent-primary) 0%,
      transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    @include smooth-transition((transform, opacity));
    z-index: 1;
    filter: blur(40px);
  }

  @include mobile-only {
    &--featured {
      .project-card__content {
        padding: $spacing-4;
      }
    }

    &__content {
      padding: $spacing-4;
    }

    &__title {
      font-size: $font-size-lg;
    }

    &__actions {
      gap: $spacing-2;
    }

    &__link {
      padding: $spacing-2 $spacing-3;
      font-size: $font-size-xs;
    }
  }
}

// ===== PROJECTS BACKGROUND =====
.projects-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  &__mesh {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 1px;
    opacity: 0.05;
  }

  &__dot {
    width: 1px;
    height: 1px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: mesh-dot-pulse 6s ease-in-out infinite;
    animation-delay: calc(var(--i) * 0.1s);
  }
}

// ===== KEYFRAME ANIMATIONS =====
@keyframes mesh-dot-pulse {
  0%, 100% { opacity: 0.05; transform: scale(1); }
  50% { opacity: 0.2; transform: scale(2); }
}

// ===== CONTACT SECTION =====
.contact-section {
  background: var(--bg-secondary);
  
  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-16;
    
    @include tablet-up {
      grid-template-columns: 1fr;
      gap: $spacing-12;
    }
  }
  
  .contact-info {
    &__item {
      @include flex-center;
      gap: $spacing-4;
      margin-bottom: $spacing-6;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    &__icon {
      @include flex-center;
      width: 48px;
      height: 48px;
      border-radius: $border-radius-full;
      background: var(--accent-primary);
      color: var(--btn-primary-text);
      font-size: $font-size-lg;
    }
    
    &__text {
      flex: 1;
    }
    
    &__label {
      @include body-small;
      color: var(--text-secondary);
      margin-bottom: $spacing-1;
    }
    
    &__value {
      @include body-base;
      color: var(--text-primary);
      font-weight: $font-weight-medium;
    }
  }
  
  .contact-form {
    background: var(--card-bg);
    padding: $spacing-8;
    border-radius: $border-radius-xl;
    box-shadow: var(--card-shadow);
  }
}

// ===== ADDITIONAL EXTRAVAGANT ANIMATIONS =====
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

@keyframes morphOrb {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    border-radius: 50%;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    border-radius: 60% 40% 30% 70%;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    border-radius: 30% 60% 70% 40%;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    border-radius: 40% 30% 60% 70%;
  }
}

@keyframes rotateShape {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
  }
  75% {
    transform: translateY(-25px) translateX(5px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes scrollLine {
  0% {
    opacity: 0;
    transform: scaleY(0);
  }
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform: scaleY(0);
  }
}

// Floating particles animation
.floating-particle {
  animation: floatParticle 8s ease-in-out infinite;
}

@keyframes floatParticle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-40px) translateX(10px) rotate(270deg);
    opacity: 0.9;
  }
}

// Holographic shimmer effect
.holographic-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

// ===== FLOWFEST-INSPIRED ANIMATIONS =====
@keyframes infiniteScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(5deg);
  }
  50% {
    transform: translateY(-10px) rotate(-3deg);
  }
  75% {
    transform: translateY(-15px) rotate(2deg);
  }
}

@keyframes underlineGrow {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

// ===== RESPONSIVE DESIGN FOR FLOWFEST HERO =====
@media (max-width: 768px) {
  .hero {
    &__image-strip {
      top: 15%;
      height: 80px;
    }

    &__image-item {
      flex: 0 0 150px;
    }

    &__floating-element {
      font-size: 1.5rem;
    }

    &__title-main {
      font-size: clamp(2.5rem, 10vw, 4rem);
    }

    &__title-dynamic {
      font-size: clamp(1.2rem, 5vw, 1.8rem);
      flex-direction: column;
      gap: 0.2rem;
    }

    &__subtitle {
      font-size: clamp(1rem, 3vw, 1.2rem);
      margin-bottom: 2rem;
    }

    &__cta {
      flex-direction: column;
      align-items: center;
      gap: 1rem;

      .btn {
        width: 100%;
        max-width: 280px;
      }
    }

    &__status {
      margin-bottom: 3rem;
    }
  }
}

@media (max-width: 480px) {
  .hero {
    &__container {
      padding: 0 1rem;
    }

    &__image-strip {
      height: 60px;
    }

    &__image-item {
      flex: 0 0 120px;
    }

    &__floating-element {
      font-size: 1.2rem;
    }

    &__title-main {
      font-size: clamp(2rem, 12vw, 3rem);
    }

    &__cta .btn {
      padding: 0.8rem 1.5rem;
      font-size: 0.9rem;
    }
  }
}
