// ===== EXTRAVAGANT HOME PAGE STYLES =====
.home-page {
  position: relative;
  overflow-x: hidden;
  background: #0a0a0a;
}

// ===== EXTRAVAGANT HERO SECTION =====
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: radial-gradient(ellipse at center,
    #0a0a0a 0%,
    #1a1a2e 50%,
    #16213e 100%
  );

  // Flash effect for dramatic entrance
  &__flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: radial-gradient(circle,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.3) 30%,
      transparent 70%
    );
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
  }

  // Floating particles container
  &__particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
  }

  // Interactive canvas background
  &__canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
  }

  // Animated background elements
  &__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    overflow: hidden;
  }

  // Morphing gradient orbs
  &__orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.6;
    animation: morphOrb 20s ease-in-out infinite;

    &--1 {
      width: 400px;
      height: 400px;
      top: 10%;
      left: 10%;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      animation-delay: 0s;
    }

    &--2 {
      width: 300px;
      height: 300px;
      top: 60%;
      right: 15%;
      background: linear-gradient(135deg, #45b7d1, #96ceb4);
      animation-delay: 7s;
    }

    &--3 {
      width: 500px;
      height: 500px;
      bottom: 10%;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(225deg, #feca57, #ff6b6b);
      animation-delay: 14s;
    }
  }

  // Geometric shapes
  &__shape {
    position: absolute;
    opacity: 0.1;
    border: 2px solid rgba(255, 255, 255, 0.3);

    &--triangle {
      width: 0;
      height: 0;
      border-left: 50px solid transparent;
      border-right: 50px solid transparent;
      border-bottom: 87px solid rgba(255, 255, 255, 0.1);
      top: 20%;
      right: 20%;
      animation: rotateShape 15s linear infinite;
    }

    &--circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      top: 70%;
      left: 20%;
      animation: floatShape 12s ease-in-out infinite;
    }

    &--square {
      width: 60px;
      height: 60px;
      top: 30%;
      left: 80%;
      animation: rotateShape 18s linear infinite reverse;
    }
  }

  // Grid pattern
  &__grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.5;
  }

  // Main container
  &__container {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
  }

  // Content wrapper
  &__content {
    color: white;
    position: relative;
  }

  // Morphing text display
  &__morphing-text {
    margin-bottom: 1rem;
  }

  &__greeting {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 300;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.1em;
    text-transform: uppercase;
  }

  // Main title with liquid effect
  &__title {
    font-size: clamp(4rem, 12vw, 8rem);
    font-weight: 900;
    margin-bottom: 2rem;
    line-height: 0.9;
    background: linear-gradient(
      45deg,
      #ff6b6b,
      #4ecdc4,
      #45b7d1,
      #feca57
    );
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 8s ease-in-out infinite;
    text-shadow: 0 0 50px rgba(255, 107, 107, 0.5);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      filter: blur(20px);
      opacity: 0.3;
      z-index: -1;
    }
  }

  // Liquid character styling
  .liquid-char {
    display: inline-block;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.2) rotate(5deg);
      text-shadow: 0 0 20px currentColor;
    }
  }

  // Glitch container
  &__glitch-container {
    position: relative;
    margin-bottom: 3rem;
  }

  // Subtitle with glitch effect
  &__subtitle {
    font-size: clamp(1.2rem, 3vw, 2rem);
    font-weight: 300;
    opacity: 0.9;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.5;
    letter-spacing: 0.05em;
    position: relative;
  }

  // Interactive CTA buttons
  &__cta {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;

    .btn {
      position: relative;
      overflow: hidden;
      min-width: 220px;
      padding: 1rem 2rem;
      border-radius: 50px;
      font-size: 1.1rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;
      border: none;

      &--primary {
        background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
        color: white;
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);

        &:hover {
          transform: translateY(-5px) scale(1.05);
          box-shadow: 0 20px 40px rgba(255, 107, 107, 0.5);
        }
      }

      &--secondary {
        background: transparent;
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);

        &:hover {
          transform: translateY(-5px) scale(1.05);
          border-color: #4ecdc4;
          box-shadow: 0 20px 40px rgba(78, 205, 196, 0.3);
        }
      }

      &__text {
        position: relative;
        z-index: 2;
      }

      &__icon {
        margin-left: 0.5rem;
        transition: transform 0.3s ease;
      }

      &:hover &__icon {
        transform: translateX(5px);
      }

      &__ripple {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
      }

      &:active &__ripple {
        width: 300px;
        height: 300px;
      }
    }
  }

  // Professional info
  &__info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
  }

  &__location,
  &__status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
  }

  &__location-icon {
    font-size: 1.2rem;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ecdc4;
    animation: pulse 2s infinite;
  }

  // Enhanced scroll indicator
  &__scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      color: #4ecdc4;
      transform: translateX(-50%) translateY(-5px);
    }
  }

  &__scroll-text {
    font-size: 0.9rem;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-transform: uppercase;
  }

  &__scroll-line {
    width: 1px;
    height: 30px;
    background: linear-gradient(to bottom,
      transparent,
      rgba(255, 255, 255, 0.5),
      transparent
    );
    animation: scrollLine 2s ease-in-out infinite;
  }

  &__scroll-arrow {
    width: 24px;
    height: 24px;
    animation: bounce 2s infinite;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  // Dynamic gradient overlays
  &__gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;

    &--primary {
      background: linear-gradient(
        45deg,
        rgba(255, 107, 107, 0.1) 0%,
        transparent 50%,
        rgba(78, 205, 196, 0.1) 100%
      );
    }

    &--secondary {
      background: radial-gradient(
        circle at 80% 20%,
        rgba(69, 183, 209, 0.15) 0%,
        transparent 50%
      );
    }
  }
    filter: blur(60px);
    opacity: 0.6;
    will-change: transform;

    &--1 {
      top: 10%;
      left: 5%;
      animation-delay: 0s;
    }

    &--2 {
      top: 50%;
      right: 5%;
      width: 300px;
      height: 300px;
      animation-delay: 2s;
    }

    &--3 {
      bottom: 10%;
      left: 15%;
      width: 350px;
      height: 350px;
      animation-delay: 4s;
    }

    @include mobile-only {
      &--1, &--2, &--3 {
        width: 200px;
        height: 200px;
        filter: blur(40px);
      }
    }
  }

  &__particles {
    @include absolute-fill;
    z-index: 2;
    pointer-events: none;
  }

  &__gradient-overlay {
    @include absolute-fill;
    background: radial-gradient(circle at center,
      transparent 0%,
      rgba(var(--bg-primary-rgb), 0.3) 70%,
      var(--bg-primary) 100%);
    z-index: 3;
    pointer-events: none;
  }
}

// Keyframe animations
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes underline-expand {
  to { transform: scaleX(1); }
}

// Particle styles
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  filter: blur(1px);

  &--primary {
    background: var(--accent-primary);
    box-shadow: 0 0 10px var(--accent-primary);
  }

  &--secondary {
    background: var(--accent-secondary);
    box-shadow: 0 0 10px var(--accent-secondary);
  }

  &--tertiary {
    background: var(--accent-tertiary);
    box-shadow: 0 0 10px var(--accent-tertiary);
  }
}

// ===== ABOUT SECTION =====
.about-section {
  .about-content {
    @include grid-center;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-16;
    align-items: center;
    
    @include tablet-up {
      grid-template-columns: 1fr;
      gap: $spacing-12;
      text-align: center;
    }
  }
  
  .about-text {
    &__intro {
      @include body-large;
      color: var(--text-secondary);
      margin-bottom: $spacing-6;
      line-height: $line-height-relaxed;
    }
    
    &__highlight {
      color: var(--accent-primary);
      font-weight: $font-weight-semibold;
    }
    
    &__stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-6;
      margin-top: $spacing-8;
      
      @include mobile-only {
        grid-template-columns: 1fr;
        gap: $spacing-4;
      }
    }
  }
  
  .stat-item {
    text-align: center;
    
    &__number {
      @include heading-3;
      color: var(--accent-primary);
      margin-bottom: $spacing-2;
    }
    
    &__label {
      @include body-small;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .about-visual {
    position: relative;
    
    &__image {
      width: 100%;
      max-width: 400px;
      height: 500px;
      object-fit: cover;
      border-radius: $border-radius-3xl;
      box-shadow: var(--card-hover-shadow);
    }
    
    &__decoration {
      position: absolute;
      top: -20px;
      right: -20px;
      width: 100px;
      height: 100px;
      background: var(--gradient-primary);
      border-radius: 50%;
      opacity: 0.8;
      filter: blur(20px);
    }
  }
}

// ===== SKILLS SECTION =====
.skills-section {
  position: relative;
  background: var(--bg-secondary);

  .skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: $spacing-12;
    margin-top: $spacing-16;
    position: relative;
    z-index: 2;

    @include mobile-only {
      grid-template-columns: 1fr;
      gap: $spacing-8;
      margin-top: $spacing-12;
    }
  }

  .skill-category {
    position: relative;

    &__header {
      @include flex-center;
      gap: $spacing-4;
      margin-bottom: $spacing-8;
      text-align: center;
    }

    &__icon {
      font-size: $font-size-4xl;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      animation: float 3s ease-in-out infinite;
    }

    &__title {
      @include heading-4;
      color: var(--text-primary);
      background: linear-gradient(135deg,
        var(--accent-primary),
        var(--accent-secondary));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &__skills {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: $spacing-4;

      @include mobile-only {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: $spacing-3;
      }
    }
  }
}

// ===== SKILL CARD =====
.skill-card {
  position: relative;
  padding: $spacing-6;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: $border-radius-2xl;
  text-align: center;
  cursor: pointer;
  overflow: hidden;
  @include smooth-transition((transform, box-shadow, border-color));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      var(--accent-primary) 0%,
      var(--accent-secondary) 100%);
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 30px rgba(255, 107, 107, 0.3);
    border-color: var(--accent-primary);

    &::before {
      opacity: 0.05;
    }

    .skill-card__icon {
      transform: scale(1.2) rotate(10deg);
    }

    .skill-card__glow {
      opacity: 1;
      transform: scale(1.5);
    }

    .skill-card__particles .skill-card__particle {
      animation-play-state: running;
    }
  }

  &__icon {
    font-size: $font-size-3xl;
    margin-bottom: $spacing-4;
    @include smooth-transition((transform, filter));
    position: relative;
    z-index: 3;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  &__content {
    position: relative;
    z-index: 3;
  }

  &__name {
    @include body-base;
    font-weight: $font-weight-semibold;
    color: var(--text-primary);
    margin-bottom: $spacing-3;
    @include smooth-transition(color);
  }

  &__level {
    @include flex-center;
    gap: $spacing-1;
    justify-content: center;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-primary);
    @include smooth-transition((background-color, transform));
    animation: skill-dot-pulse 2s ease-in-out infinite;
    animation-play-state: paused;

    &--active {
      background: var(--accent-primary);
      box-shadow: 0 0 8px var(--accent-primary);
      animation-play-state: running;
    }
  }

  &__glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle,
      var(--accent-primary) 0%,
      transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    @include smooth-transition((transform, opacity));
    z-index: 2;
    filter: blur(20px);
  }

  &__particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 2;
  }

  &__particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-primary);
    border-radius: 50%;
    opacity: 0;
    animation: skill-particle-float 3s ease-in-out infinite;
    animation-play-state: paused;

    &:nth-child(1) {
      top: 20%;
      left: 20%;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      top: 30%;
      right: 25%;
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      bottom: 25%;
      left: 30%;
      animation-delay: 1s;
    }
  }

  // Category-specific styling
  &--frontend {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(255, 107, 107, 0.4);
    }
  }

  &--backend {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(78, 205, 196, 0.4);
    }
  }

  &--tools {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(69, 183, 209, 0.4);
    }
  }

  &--design {
    &:hover {
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 30px rgba(254, 202, 87, 0.4);
    }
  }

  @include mobile-only {
    padding: $spacing-4;

    &__icon {
      font-size: $font-size-2xl;
      margin-bottom: $spacing-3;
    }

    &__name {
      font-size: $font-size-sm;
    }

    &__dot {
      width: 6px;
      height: 6px;
    }
  }
}

// ===== SKILLS BACKGROUND =====
.skills-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  &__grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 2px;
    opacity: 0.1;
  }

  &__dot {
    width: 2px;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: grid-dot-pulse 4s ease-in-out infinite;
    animation-delay: calc(var(--i) * 0.1s);
  }

  &__lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &__line {
    position: absolute;
    background: linear-gradient(90deg,
      transparent,
      var(--accent-primary),
      transparent);
    height: 1px;
    width: 100%;
    opacity: 0.1;
    animation: line-move 8s ease-in-out infinite;

    &:nth-child(1) { top: 20%; animation-delay: 0s; }
    &:nth-child(2) { top: 40%; animation-delay: 1s; }
    &:nth-child(3) { top: 60%; animation-delay: 2s; }
    &:nth-child(4) { top: 80%; animation-delay: 3s; }
    &:nth-child(5) { top: 100%; animation-delay: 4s; }
  }
}

// ===== KEYFRAME ANIMATIONS =====
@keyframes skill-dot-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

@keyframes skill-particle-float {
  0% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-20px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-40px) scale(0);
  }
}

@keyframes grid-dot-pulse {
  0%, 100% { opacity: 0.1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.5); }
}

@keyframes line-move {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

// ===== TIMELINE SECTION =====
.timeline-section {
  .timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding-left: $spacing-8;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--border-primary);
    }
    
    @include mobile-only {
      padding-left: $spacing-6;
    }
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: $spacing-12;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &::before {
      content: '';
      position: absolute;
      left: -$spacing-8;
      top: $spacing-6;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--accent-primary);
      border: 3px solid var(--bg-primary);
      
      @include mobile-only {
        left: -$spacing-6;
      }
    }
  }
}

// ===== PROJECTS SECTION =====
.projects-section {
  position: relative;
  background: var(--bg-primary);

  .projects-featured {
    display: grid;
    gap: $spacing-12;
    margin-top: $spacing-16;

    @include mobile-only {
      gap: $spacing-8;
      margin-top: $spacing-12;
    }
  }

  .projects-divider {
    @include flex-center;
    gap: $spacing-6;
    margin: $spacing-20 0 $spacing-16;

    &__title {
      @include heading-4;
      color: var(--text-secondary);
      white-space: nowrap;
    }

    &__line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg,
        transparent,
        var(--border-primary),
        transparent);
    }

    @include mobile-only {
      margin: $spacing-16 0 $spacing-12;
    }
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: $spacing-8;

    @include mobile-only {
      grid-template-columns: 1fr;
      gap: $spacing-6;
    }
  }
}

// ===== PROJECT CARD =====
.project-card {
  position: relative;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: $border-radius-2xl;
  overflow: hidden;
  cursor: pointer;
  @include smooth-transition((transform, box-shadow, border-color));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      var(--accent-primary) 0%,
      var(--accent-secondary) 100%);
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-12px);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 40px rgba(255, 107, 107, 0.2);
    border-color: var(--accent-primary);

    &::before {
      opacity: 0.03;
    }

    .project-card__image {
      transform: scale(1.05);
    }

    .project-card__overlay {
      opacity: 1;
    }

    .project-card__glow {
      opacity: 1;
      transform: scale(1.2);
    }

    .project-card__category {
      transform: translateY(-5px);
      background: var(--accent-primary);
      color: var(--btn-primary-text);
    }
  }

  &--featured {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;

    @include tablet-up {
      grid-template-columns: 1fr;
    }

    .project-card__image-container {
      order: 1;

      @include tablet-up {
        order: 0;
        height: 300px;
      }
    }

    .project-card__content {
      order: 0;
      padding: $spacing-8;
      @include flex-column;
      justify-content: center;

      @include tablet-up {
        order: 1;
        padding: $spacing-6;
      }
    }
  }

  &--expanded {
    .project-card__description {
      max-height: none;
      -webkit-line-clamp: unset;
    }

    .project-card__tech-stack {
      max-height: none;
      overflow: visible;
    }
  }

  &__image-container {
    position: relative;
    height: 250px;
    overflow: hidden;

    .project-card--featured & {
      height: 100%;
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    @include smooth-transition(transform);
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    @include flex-center;
    opacity: 0;
    @include smooth-transition(opacity);
    z-index: 2;
  }

  &__overlay-content {
    @include flex-center;
    @include flex-column;
    gap: $spacing-3;
    color: white;
    text-align: center;
  }

  &__view-text {
    @include body-large;
    font-weight: $font-weight-semibold;
  }

  &__arrow {
    font-size: $font-size-2xl;
    @include smooth-transition(transform);

    .project-card:hover & {
      transform: translateX(5px);
    }
  }

  &__category {
    position: absolute;
    top: $spacing-4;
    right: $spacing-4;
    padding: $spacing-2 $spacing-4;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: $border-radius-full;
    font-size: $font-size-xs;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    @include smooth-transition((transform, background-color, color));
    z-index: 3;
  }

  &__content {
    padding: $spacing-6;
    position: relative;
    z-index: 2;
  }

  &__header {
    margin-bottom: $spacing-4;
  }

  &__title {
    @include heading-4;
    color: var(--text-primary);
    margin-bottom: $spacing-2;

    .project-card--featured & {
      @include heading-3;
      margin-bottom: $spacing-3;
    }
  }

  &__subtitle {
    @include body-base;
    color: var(--accent-primary);
    font-weight: $font-weight-semibold;
  }

  &__description {
    @include body-base;
    color: var(--text-secondary);
    line-height: $line-height-relaxed;
    margin-bottom: $spacing-6;
    max-height: 4.5em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    @include smooth-transition(max-height);

    .project-card--featured & {
      @include body-large;
      max-height: 6em;
      -webkit-line-clamp: 4;
    }
  }

  &__tech-stack {
    @include flex-center;
    flex-wrap: wrap;
    gap: $spacing-2;
    margin-bottom: $spacing-6;
    max-height: 3em;
    overflow: hidden;
    @include smooth-transition(max-height);
  }

  &__tech-tag {
    padding: $spacing-1 $spacing-3;
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
    border-radius: $border-radius-full;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    @include smooth-transition((background-color, color));

    &--more {
      background: var(--accent-primary);
      color: var(--btn-primary-text);
    }

    .project-card:hover & {
      background: var(--hover-bg);
      color: var(--text-primary);

      &--more {
        background: var(--accent-secondary);
      }
    }
  }

  &__actions {
    @include flex-center;
    gap: $spacing-3;
    flex-wrap: wrap;
  }

  &__link {
    @include flex-center;
    gap: $spacing-2;
    padding: $spacing-2 $spacing-4;
    background: var(--hover-bg);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: $border-radius-lg;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    @include smooth-transition((background-color, color, transform));

    svg {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: var(--active-bg);
      color: var(--text-primary);
      transform: translateY(-2px);
    }

    &--primary {
      background: var(--accent-primary);
      color: var(--btn-primary-text);

      &:hover {
        background: var(--accent-secondary);
        color: var(--btn-primary-text);
      }
    }
  }

  &__glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle,
      var(--accent-primary) 0%,
      transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    @include smooth-transition((transform, opacity));
    z-index: 1;
    filter: blur(40px);
  }

  @include mobile-only {
    &--featured {
      .project-card__content {
        padding: $spacing-4;
      }
    }

    &__content {
      padding: $spacing-4;
    }

    &__title {
      font-size: $font-size-lg;
    }

    &__actions {
      gap: $spacing-2;
    }

    &__link {
      padding: $spacing-2 $spacing-3;
      font-size: $font-size-xs;
    }
  }
}

// ===== PROJECTS BACKGROUND =====
.projects-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  &__mesh {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 1px;
    opacity: 0.05;
  }

  &__dot {
    width: 1px;
    height: 1px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: mesh-dot-pulse 6s ease-in-out infinite;
    animation-delay: calc(var(--i) * 0.1s);
  }
}

// ===== KEYFRAME ANIMATIONS =====
@keyframes mesh-dot-pulse {
  0%, 100% { opacity: 0.05; transform: scale(1); }
  50% { opacity: 0.2; transform: scale(2); }
}

// ===== CONTACT SECTION =====
.contact-section {
  background: var(--bg-secondary);
  
  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-16;
    
    @include tablet-up {
      grid-template-columns: 1fr;
      gap: $spacing-12;
    }
  }
  
  .contact-info {
    &__item {
      @include flex-center;
      gap: $spacing-4;
      margin-bottom: $spacing-6;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    &__icon {
      @include flex-center;
      width: 48px;
      height: 48px;
      border-radius: $border-radius-full;
      background: var(--accent-primary);
      color: var(--btn-primary-text);
      font-size: $font-size-lg;
    }
    
    &__text {
      flex: 1;
    }
    
    &__label {
      @include body-small;
      color: var(--text-secondary);
      margin-bottom: $spacing-1;
    }
    
    &__value {
      @include body-base;
      color: var(--text-primary);
      font-weight: $font-weight-medium;
    }
  }
  
  .contact-form {
    background: var(--card-bg);
    padding: $spacing-8;
    border-radius: $border-radius-xl;
    box-shadow: var(--card-shadow);
  }
}

// ===== ADDITIONAL EXTRAVAGANT ANIMATIONS =====
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

@keyframes morphOrb {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    border-radius: 50%;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    border-radius: 60% 40% 30% 70%;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    border-radius: 30% 60% 70% 40%;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    border-radius: 40% 30% 60% 70%;
  }
}

@keyframes rotateShape {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
  }
  75% {
    transform: translateY(-25px) translateX(5px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes scrollLine {
  0% {
    opacity: 0;
    transform: scaleY(0);
  }
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform: scaleY(0);
  }
}

// Floating particles animation
.floating-particle {
  animation: floatParticle 8s ease-in-out infinite;
}

@keyframes floatParticle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-40px) translateX(10px) rotate(270deg);
    opacity: 0.9;
  }
}

// Holographic shimmer effect
.holographic-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}
