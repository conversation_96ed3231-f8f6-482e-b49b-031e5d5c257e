import { useRef, useLayoutEffect, useState, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { personalInfo } from '../../data/portfolio';

gsap.registerPlugin(ScrollTrigger);

export const Hero: React.FC = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);

  const imageStripRef = useRef<HTMLDivElement>(null);
  const floatingElementsRef = useRef<HTMLDivElement>(null);
  const magneticButtonsRef = useRef<HTMLDivElement>(null);

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // Dynamic words for the morphing text effect
  const dynamicWords = [
    'Developer',
    'Designer',
    'Creator',
    'Innovator',
    'Problem Solver',
    'Digital Artist'
  ];

  // Mouse tracking for magnetic effects
  useLayoutEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: (e.clientY / window.innerHeight) * 2 - 1
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Dynamic word cycling effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % dynamicWords.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [dynamicWords.length]);

  // Flowfest-inspired entrance animations
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    const ctx = gsap.context(() => {
      // Set initial states
      gsap.set('.hero__title-main', { y: 100, opacity: 0 });
      gsap.set('.hero__title-dynamic', { y: 50, opacity: 0 });
      gsap.set('.hero__subtitle', { y: 30, opacity: 0 });
      gsap.set('.hero__cta', { y: 40, opacity: 0 });
      gsap.set('.hero__floating-element', { scale: 0, rotation: 45 });
      gsap.set('.hero__image-strip', { x: '-100%' });

      // Main entrance timeline
      const tl = gsap.timeline({ delay: 0.5 });

      // 1. Title entrance with bounce
      tl.to('.hero__title-main', {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: 'back.out(1.7)'
      })
      // 2. Dynamic word with elastic effect
      .to('.hero__title-dynamic', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'elastic.out(1, 0.5)'
      }, '-=0.6')
      // 3. Subtitle slide up
      .to('.hero__subtitle', {
        y: 0,
        opacity: 1,
        duration: 0.6,
        ease: 'power2.out'
      }, '-=0.4')
      // 4. CTA buttons with stagger
      .to('.hero__cta', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'power2.out'
      }, '-=0.3')
      // 5. Floating elements pop in
      .to('.hero__floating-element', {
        scale: 1,
        rotation: 0,
        duration: 0.6,
        ease: 'back.out(1.7)',
        stagger: 0.1
      }, '-=0.5')
      // 6. Image strip slide in
      .to('.hero__image-strip', {
        x: '0%',
        duration: 1.5,
        ease: 'power2.out'
      }, '-=0.8');

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Continuous animations
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    const ctx = gsap.context(() => {
      // Floating animation for main content
      gsap.to('.hero__content', {
        y: -10,
        duration: 3,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });

      // Rotating floating elements
      gsap.to('.hero__floating-element', {
        rotation: 360,
        duration: 20,
        ease: 'none',
        repeat: -1,
        stagger: 2
      });

      // Infinite image strip scroll
      gsap.to('.hero__image-item', {
        x: '-100%',
        duration: 15,
        ease: 'none',
        repeat: -1,
        stagger: 0
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Magnetic button effects
  useLayoutEffect(() => {
    if (!magneticButtonsRef.current) return;

    const buttons = magneticButtonsRef.current.querySelectorAll('.btn');

    buttons.forEach(button => {
      const handleMouseMove = (e: MouseEvent) => {
        const rect = (button as HTMLElement).getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const deltaX = (e.clientX - centerX) * 0.3;
        const deltaY = (e.clientY - centerY) * 0.3;

        gsap.to(button, {
          x: deltaX,
          y: deltaY,
          duration: 0.3,
          ease: 'power2.out'
        });
      };

      const handleMouseLeave = () => {
        gsap.to(button, {
          x: 0,
          y: 0,
          duration: 0.5,
          ease: 'elastic.out(1, 0.3)'
        });
      };

      (button as HTMLElement).addEventListener('mousemove', handleMouseMove);
      (button as HTMLElement).addEventListener('mouseleave', handleMouseLeave);
    });
  }, []);

  // Parallax effect for floating elements
  useLayoutEffect(() => {
    if (!floatingElementsRef.current) return;

    const elements = floatingElementsRef.current.querySelectorAll('.hero__floating-element');

    const handleMouseMove = () => {
      elements.forEach((el, index) => {
        const speed = (index + 1) * 0.5;
        gsap.to(el, {
          x: mousePosition.x * speed * 10,
          y: mousePosition.y * speed * 10,
          duration: 1,
          ease: 'power2.out'
        });
      });
    };

    handleMouseMove();
  }, [mousePosition]);

  // Scroll-triggered animations
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    ScrollTrigger.create({
      trigger: heroRef.current,
      start: 'top top',
      end: 'bottom top',
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // Parallax scroll effect
        gsap.to('.hero__content', {
          y: progress * -100,
          scale: 1 - progress * 0.1,
          duration: 0.3
        });

        gsap.to('.hero__floating-element', {
          y: progress * -150,
          rotation: progress * 180,
          duration: 0.3
        });
      }
    });

    return () => ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }, []);

  return (
    <section ref={heroRef} className="hero">
      {/* Continuous Image Strip */}
      <div ref={imageStripRef} className="hero__image-strip">
        <div className="hero__image-track">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="hero__image-item">
              <div className="hero__image-placeholder">
                <span>Project {i + 1}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Floating Elements */}
      <div ref={floatingElementsRef} className="hero__floating-elements">
        <div className="hero__floating-element hero__floating-element--1">✦</div>
        <div className="hero__floating-element hero__floating-element--2">◆</div>
        <div className="hero__floating-element hero__floating-element--3">●</div>
        <div className="hero__floating-element hero__floating-element--4">▲</div>
        <div className="hero__floating-element hero__floating-element--5">★</div>
      </div>

      {/* Main Content */}
      <div className="hero__container">
        <div className="hero__content">
          {/* Flowfest-style Title */}
          <div className="hero__title-wrapper">
            <h1 className="hero__title-main" ref={titleRef}>
              Hey, I'm {personalInfo.name.split(' ')[0]}
            </h1>
            <div className="hero__title-dynamic">
              <span className="hero__title-prefix">I'm a </span>
              <span className="hero__title-word" key={currentWordIndex}>
                {dynamicWords[currentWordIndex]}
              </span>
            </div>
          </div>

          {/* Subtitle */}
          <p ref={subtitleRef} className="hero__subtitle">
            {personalInfo.subtitle}
          </p>

          {/* Magnetic CTA Buttons */}
          <div ref={magneticButtonsRef} className="hero__cta">
            <button className="btn btn--primary btn--magnetic">
              <span>View My Work</span>
              <div className="btn__bg"></div>
            </button>
            <button className="btn btn--secondary btn--magnetic">
              <span>Let's Chat</span>
              <div className="btn__bg"></div>
            </button>
          </div>

          {/* Status Badge */}
          <div className="hero__status">
            <div className="hero__status-dot"></div>
            <span>Available for freelance work</span>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="hero__scroll">
          <span>Scroll to explore</span>
          <div className="hero__scroll-arrow">↓</div>
        </div>
      </div>

      {/* Background Gradient */}
      <div className="hero__gradient"></div>
    </section>
  );
};
