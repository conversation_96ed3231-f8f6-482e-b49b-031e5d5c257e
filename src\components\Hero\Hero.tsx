import { useRef, useLayoutEffect } from 'react';
import { gsap } from 'gsap';
import { personalInfo } from '../../data/portfolio';

export const Hero: React.FC = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Blob animation refs
  const blob1Ref = useRef<SVGPathElement>(null);
  const blob2Ref = useRef<SVGPathElement>(null);
  const blob3Ref = useRef<SVGPathElement>(null);

  // Particle container ref
  const particleRef = useRef<HTMLDivElement>(null);

  // Main hero animation
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    const ctx = gsap.context(() => {
      // Set initial states
      gsap.set('.hero__title-word', {
        opacity: 0,
        y: 100
      });

      gsap.set('.hero__subtitle', {
        opacity: 0,
        y: 50
      });

      gsap.set('.hero__cta', {
        opacity: 0,
        y: 30
      });

      gsap.set('.hero__scroll-indicator', {
        opacity: 0,
        y: 20
      });

      // Create the main timeline
      const tl = gsap.timeline();

      tl.to('.hero__title-word', {
        opacity: 1,
        y: 0,
        duration: 1.2,
        ease: 'back.out(1.7)',
        stagger: 0.1
      })
      .to('.hero__subtitle', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out'
      }, '-=0.6')
      .to('.hero__cta', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'power2.out'
      }, '-=0.4')
      .to('.hero__scroll-indicator', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: 'power2.out'
      }, '-=0.2');

      // Floating animation for the entire hero content
      gsap.to('.hero__content', {
        y: -10,
        duration: 3,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Background blob animations
  useLayoutEffect(() => {
    const blobs = document.querySelectorAll('.hero__blob');

    blobs.forEach((blob, index) => {
      gsap.to(blob, {
        x: `+=${gsap.utils.random(-50, 50)}`,
        y: `+=${gsap.utils.random(-30, 30)}`,
        rotation: `+=${gsap.utils.random(-180, 180)}`,
        duration: gsap.utils.random(8, 12),
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true,
        delay: index * 2
      });
    });

    // Animate blob paths
    if (blob1Ref.current) {
      gsap.to(blob1Ref.current, {
        scaleX: 1.2,
        scaleY: 0.8,
        rotation: 45,
        duration: 8,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });
    }

    if (blob2Ref.current) {
      gsap.to(blob2Ref.current, {
        scaleX: 0.9,
        scaleY: 1.3,
        rotation: -30,
        duration: 12,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });
    }

    if (blob3Ref.current) {
      gsap.to(blob3Ref.current, {
        scaleX: 1.1,
        scaleY: 0.9,
        rotation: 60,
        duration: 10,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });
    }
  }, []);

  const splitTitle = (text: string) => {
    return text.split(' ').map((word, index) => (
      <span key={index} className="hero__title-word">
        {word}
        {index < text.split(' ').length - 1 && ' '}
      </span>
    ));
  };

  return (
    <section ref={heroRef} className="hero">
      {/* Particle System */}
      <div ref={particleRef} className="hero__particles" />
      
      {/* Morphing Background Blobs */}
      <div className="hero__background">
        <svg className="hero__blob hero__blob--1" viewBox="0 0 200 200">
          <path
            ref={blob1Ref}
            d="M60,-60C80,-40,100,-20,100,0C100,20,80,40,60,60C40,80,20,100,0,100C-20,100,-40,80,-60,60C-80,40,-100,20,-100,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-100C20,-100,40,-80,60,-60Z"
            fill="url(#gradient1)"
          />
          <defs>
            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ff6b6b" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#4ecdc4" stopOpacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
        
        <svg className="hero__blob hero__blob--2" viewBox="0 0 200 200">
          <path
            ref={blob2Ref}
            d="M40,-60C60,-50,80,-30,90,-10C100,10,90,30,70,40C50,50,20,50,-10,50C-40,50,-70,40,-80,20C-90,0,-80,-20,-60,-40C-40,-60,-20,-80,0,-80C20,-80,20,-70,40,-60Z"
            fill="url(#gradient2)"
          />
          <defs>
            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#45b7d1" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#96ceb4" stopOpacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
        
        <svg className="hero__blob hero__blob--3" viewBox="0 0 200 200">
          <path
            ref={blob3Ref}
            d="M50,-70C70,-60,90,-40,95,-15C100,10,90,35,70,50C50,65,20,70,-10,70C-40,70,-70,65,-85,45C-100,25,-100,-5,-90,-30C-80,-55,-60,-75,-35,-80C-10,-85,15,-75,35,-65C55,-55,30,-80,50,-70Z"
            fill="url(#gradient3)"
          />
          <defs>
            <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#feca57" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#ff6b6b" stopOpacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      {/* Main Content */}
      <div className="hero__container">
        <div className="hero__content">
          <h1 ref={titleRef} className="hero__title">
            {splitTitle(`Hello, I'm ${personalInfo.name.split(' ')[0]}`)}
          </h1>
          
          <p ref={subtitleRef} className="hero__subtitle">
            {personalInfo.subtitle}
          </p>
          
          <div ref={ctaRef} className="hero__cta">
            <button className="btn btn--primary btn--large hero__cta-primary">
              View My Work
              <span className="btn__icon">→</span>
            </button>

            <button className="btn btn--secondary btn--large hero__cta-secondary">
              Get In Touch
              <span className="btn__icon">✉</span>
            </button>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="hero__scroll-indicator">
          <div className="hero__scroll-text">Scroll to explore</div>
          <div className="hero__scroll-arrow">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 5v14M19 12l-7 7-7-7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
      
      {/* Gradient Overlay */}
      <div className="hero__gradient-overlay" />
    </section>
  );
};
