import { useRef, useLayoutEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { personalInfo } from '../../data/portfolio';
import {
  liquidText,
  holographicShimmer,
  floatingParticles,
  textScramble,
  magneticField
} from '../../animations/gsapUtils';

gsap.registerPlugin(ScrollTrigger);

export const Hero: React.FC = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particleContainerRef = useRef<HTMLDivElement>(null);
  const morphingTextRef = useRef<HTMLDivElement>(null);
  const glitchTextRef = useRef<HTMLDivElement>(null);

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  // Mouse tracking for interactive effects
  useLayoutEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: (e.clientY / window.innerHeight) * 2 - 1
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Main hero animation with extravagant effects
  useLayoutEffect(() => {
    if (!heroRef.current || !titleRef.current || !subtitleRef.current) return;

    const ctx = gsap.context(() => {
      // Create master timeline
      const masterTl = gsap.timeline({
        onComplete: () => setIsLoaded(true)
      });

      // Initial setup - hide everything
      gsap.set(['.hero__title', '.hero__subtitle', '.hero__cta', '.hero__scroll'], {
        opacity: 0
      });

      // 1. Dramatic entrance with screen flash
      masterTl.fromTo('.hero__flash', {
        opacity: 0,
        scale: 0
      }, {
        opacity: 1,
        scale: 3,
        duration: 0.3,
        ease: 'power2.out'
      })
      .to('.hero__flash', {
        opacity: 0,
        duration: 0.5,
        ease: 'power2.inOut'
      }, '-=0.1');

      // 2. Liquid text animation for title
      masterTl.call(() => {
        if (titleRef.current) {
          liquidText(titleRef.current, {
            text: `Hello, I'm ${personalInfo.name.split(' ')[0]}`,
            duration: 1.5,
            stagger: 0.05
          });
        }
      }, [], 0.5);

      // 3. Glitch effect on subtitle
      masterTl.call(() => {
        if (subtitleRef.current) {
          textScramble(subtitleRef.current, {
            text: personalInfo.subtitle,
            duration: 2
          });
        }
      }, [], 1);

      // 4. Holographic shimmer on CTA buttons
      masterTl.call(() => {
        const buttons = document.querySelectorAll('.hero__cta .btn');
        buttons.forEach(btn => {
          holographicShimmer(btn, {
            duration: 2,
            intensity: 0.7
          });
        });
      }, [], 1.5);

      // 5. Reveal elements with dramatic effects
      masterTl.to('.hero__title', {
        opacity: 1,
        duration: 0.1
      }, 0.5)
      .to('.hero__subtitle', {
        opacity: 1,
        duration: 0.1
      }, 1)
      .to('.hero__cta', {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'back.out(1.7)'
      }, 1.5)
      .to('.hero__scroll', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power2.out'
      }, 2);

      // 6. Continuous floating animation
      masterTl.to('.hero__content', {
        y: -15,
        duration: 4,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      }, 2);

    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Floating particles and interactive effects
  useLayoutEffect(() => {
    if (!particleContainerRef.current || !isLoaded) return;

    // Create floating particles
    floatingParticles(particleContainerRef.current, {
      count: 50,
      colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#feca57', '#96ceb4'],
      size: { min: 2, max: 8 },
      speed: { min: 30, max: 80 }
    });

    // Add magnetic field effect to buttons
    magneticField(heroRef.current!, {
      strength: 0.4,
      distance: 120,
      duration: 0.4
    });

  }, [isLoaded]);

  // Parallax effect based on mouse movement
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    const elements = heroRef.current.querySelectorAll('[data-parallax]');

    elements.forEach(el => {
      const speed = parseFloat((el as HTMLElement).dataset.parallax || '1');

      gsap.to(el, {
        x: mousePosition.x * speed * 20,
        y: mousePosition.y * speed * 20,
        duration: 1,
        ease: 'power2.out'
      });
    });
  }, [mousePosition]);

  // Scroll-triggered animations
  useLayoutEffect(() => {
    if (!heroRef.current) return;

    ScrollTrigger.create({
      trigger: heroRef.current,
      start: 'top top',
      end: 'bottom top',
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // Parallax background elements
        gsap.to('.hero__bg-element', {
          y: progress * 200,
          opacity: 1 - progress,
          duration: 0.3
        });

        // Scale and fade hero content
        gsap.to('.hero__content', {
          scale: 1 - progress * 0.2,
          opacity: 1 - progress * 0.5,
          duration: 0.3
        });
      }
    });

    return () => ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }, []);

  return (
    <section ref={heroRef} className="hero">
      {/* Flash Effect for Dramatic Entrance */}
      <div className="hero__flash"></div>

      {/* Floating Particles Container */}
      <div ref={particleContainerRef} className="hero__particles"></div>

      {/* Interactive Canvas Background */}
      <canvas ref={canvasRef} className="hero__canvas"></canvas>

      {/* Animated Background Elements */}
      <div className="hero__background">
        {/* Morphing Gradient Orbs */}
        <div className="hero__orb hero__orb--1" data-parallax="2"></div>
        <div className="hero__orb hero__orb--2" data-parallax="1.5"></div>
        <div className="hero__orb hero__orb--3" data-parallax="3"></div>

        {/* Geometric Shapes */}
        <div className="hero__shape hero__shape--triangle" data-parallax="1"></div>
        <div className="hero__shape hero__shape--circle" data-parallax="2.5"></div>
        <div className="hero__shape hero__shape--square" data-parallax="1.8"></div>

        {/* Grid Pattern */}
        <div className="hero__grid" data-parallax="0.5"></div>
      </div>

      {/* Main Content */}
      <div className="hero__container">
        <div className="hero__content" data-parallax="0.5">
          {/* Morphing Text Display */}
          <div ref={morphingTextRef} className="hero__morphing-text">
            <span className="hero__greeting">Hello, I'm</span>
          </div>

          {/* Main Title with Liquid Effect */}
          <h1 ref={titleRef} className="hero__title">
            {personalInfo.name.split(' ')[0]}
          </h1>

          {/* Glitch Text Subtitle */}
          <div ref={glitchTextRef} className="hero__glitch-container">
            <p ref={subtitleRef} className="hero__subtitle">
              {personalInfo.subtitle}
            </p>
          </div>

          {/* Interactive CTA Buttons */}
          <div ref={ctaRef} className="hero__cta">
            <button
              className="btn btn--primary btn--large hero__cta-primary"
              data-magnetic
            >
              <span className="btn__text">View My Work</span>
              <span className="btn__icon">→</span>
              <div className="btn__ripple"></div>
            </button>

            <button
              className="btn btn--secondary btn--large hero__cta-secondary"
              data-magnetic
            >
              <span className="btn__text">Get In Touch</span>
              <span className="btn__icon">✉</span>
              <div className="btn__ripple"></div>
            </button>
          </div>

          {/* Professional Info */}
          <div className="hero__info">
            <div className="hero__location" data-parallax="1.2">
              <span className="hero__location-icon">📍</span>
              <span className="hero__location-text">{personalInfo.location}</span>
            </div>
            <div className="hero__status" data-parallax="1.5">
              <span className="hero__status-dot"></span>
              <span className="hero__status-text">Available for work</span>
            </div>
          </div>
        </div>

        {/* Enhanced Scroll Indicator */}
        <div className="hero__scroll" data-parallax="2">
          <div className="hero__scroll-text">Scroll to explore</div>
          <div className="hero__scroll-line"></div>
          <div className="hero__scroll-arrow">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 5v14M19 12l-7 7-7-7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>

      {/* Dynamic Gradient Overlays */}
      <div className="hero__gradient-overlay hero__gradient-overlay--primary"></div>
      <div className="hero__gradient-overlay hero__gradient-overlay--secondary"></div>
    </section>
  );
};
