import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';
import { Flip } from 'gsap/Flip';

// Register GSAP plugins (only free ones for now)
gsap.registerPlugin(ScrollTrigger, TextPlugin, Flip);

// ===== ANIMATION PRESETS =====
export const ANIMATION_PRESETS = {
  // Easing curves
  ease: {
    power1: 'power1.out',
    power2: 'power2.out',
    power3: 'power3.out',
    power4: 'power4.out',
    back: 'back.out(1.7)',
    elastic: 'elastic.out(1, 0.3)',
    bounce: 'bounce.out',
    expo: 'expo.out',
    circ: 'circ.out',
    sine: 'sine.out',
    custom: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  },
  
  // Duration presets
  duration: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.2,
    slower: 1.8,
    crawl: 2.5,
  },
  
  // Stagger presets
  stagger: {
    fast: 0.05,
    normal: 0.1,
    slow: 0.2,
    slower: 0.3,
  }
};

// ===== CORE ANIMATION FUNCTIONS =====

/**
 * Fade in animation with customizable direction
 */
export const fadeIn = (
  element: string | Element | Element[],
  options: {
    direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'none';
    duration?: number;
    delay?: number;
    distance?: number;
    ease?: string;
    stagger?: number;
  } = {}
) => {
  const {
    direction = 'up',
    duration = ANIMATION_PRESETS.duration.normal,
    delay = 0,
    distance = 50,
    ease = ANIMATION_PRESETS.ease.power2,
    stagger = 0
  } = options;

  const fromVars: any = { opacity: 0 };
  const toVars: any = { opacity: 1, duration, delay, ease };

  // Set initial transform based on direction
  switch (direction) {
    case 'up':
      fromVars.y = distance;
      toVars.y = 0;
      break;
    case 'down':
      fromVars.y = -distance;
      toVars.y = 0;
      break;
    case 'left':
      fromVars.x = -distance;
      toVars.x = 0;
      break;
    case 'right':
      fromVars.x = distance;
      toVars.x = 0;
      break;
    case 'scale':
      fromVars.scale = 0.8;
      toVars.scale = 1;
      break;
  }

  if (stagger > 0) {
    toVars.stagger = stagger;
  }

  gsap.set(element, fromVars);
  return gsap.to(element, toVars);
};

/**
 * Advanced text reveal animation
 */
export const textReveal = (
  element: string | Element,
  options: {
    duration?: number;
    stagger?: number;
    ease?: string;
    delay?: number;
  } = {}
) => {
  const {
    duration = ANIMATION_PRESETS.duration.normal,
    stagger = ANIMATION_PRESETS.stagger.normal,
    ease = ANIMATION_PRESETS.ease.power2,
    delay = 0
  } = options;

  const tl = gsap.timeline({ delay });
  const el = typeof element === 'string' ? document.querySelector(element) : element;

  if (!el) return tl;

  // Simple fallback text splitting
  const text = el.textContent || '';
  const words = text.split(' ');

  // Clear original text and create spans
  el.innerHTML = '';
  const spans: HTMLElement[] = [];

  words.forEach((word) => {
    const span = document.createElement('span');
    span.textContent = word;
    span.style.display = 'inline-block';
    span.style.marginRight = '0.25em';
    el.appendChild(span);
    spans.push(span);
  });

  // Set initial state
  gsap.set(spans, {
    opacity: 0,
    y: 100,
    rotationX: -90,
    transformOrigin: '50% 50% -50px'
  });

  // Animate in
  tl.to(spans, {
    opacity: 1,
    y: 0,
    rotationX: 0,
    duration,
    ease,
    stagger
  });

  return tl;
};

/**
 * Morphing blob animation (using scale and rotation as fallback)
 */
export const morphingBlob = (
  element: string | Element,
  options: {
    duration?: number;
    ease?: string;
    repeat?: number;
    yoyo?: boolean;
  } = {}
) => {
  const {
    duration = 8,
    ease = ANIMATION_PRESETS.ease.sine,
    repeat = -1,
    yoyo = true
  } = options;

  // Fallback animation using scale and rotation
  return gsap.to(element, {
    scaleX: 1.2,
    scaleY: 0.8,
    rotation: 45,
    duration,
    ease,
    repeat,
    yoyo
  });
};

/**
 * Particle system animation
 */
export const particleSystem = (
  container: string | Element,
  options: {
    count?: number;
    colors?: string[];
    size?: { min: number; max: number };
    speed?: { min: number; max: number };
    life?: { min: number; max: number };
  } = {}
) => {
  const {
    count = 50,
    colors = ['#ff6b6b', '#4ecdc4', '#45b7d1'],
    size = { min: 2, max: 8 },
    speed = { min: 1, max: 3 },
    life = { min: 3, max: 6 }
  } = options;

  const particles: HTMLElement[] = [];
  const containerEl = typeof container === 'string' ? document.querySelector(container) : container;
  
  if (!containerEl) return;

  // Create particles
  for (let i = 0; i < count; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.cssText = `
      position: absolute;
      border-radius: 50%;
      pointer-events: none;
      background: ${colors[Math.floor(Math.random() * colors.length)]};
      width: ${gsap.utils.random(size.min, size.max)}px;
      height: ${gsap.utils.random(size.min, size.max)}px;
      opacity: 0;
    `;
    
    containerEl.appendChild(particle);
    particles.push(particle);
    
    // Animate particle
    animateParticle(particle, speed, life);
  }

  return particles;
};

const animateParticle = (
  particle: HTMLElement,
  _speed: { min: number; max: number },
  life: { min: number; max: number }
) => {
  const tl = gsap.timeline({ repeat: -1 });
  
  tl.set(particle, {
    x: gsap.utils.random(0, window.innerWidth),
    y: window.innerHeight + 50,
    opacity: 0
  })
  .to(particle, {
    opacity: 1,
    duration: 0.5,
    ease: ANIMATION_PRESETS.ease.power2
  })
  .to(particle, {
    y: -50,
    x: `+=${gsap.utils.random(-100, 100)}`,
    duration: gsap.utils.random(life.min, life.max),
    ease: 'none'
  }, '-=0.3')
  .to(particle, {
    opacity: 0,
    duration: 0.5,
    ease: ANIMATION_PRESETS.ease.power2
  }, '-=1');
};

/**
 * Magnetic hover effect
 */
export const magneticHover = (
  element: string | Element,
  options: {
    strength?: number;
    speed?: number;
  } = {}
) => {
  const { strength = 0.3, speed = 0.3 } = options;
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  
  if (!el) return;

  const handleMouseMove = (e: Event) => {
    const mouseEvent = e as MouseEvent;
    const rect = (el as HTMLElement).getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const deltaX = (mouseEvent.clientX - centerX) * strength;
    const deltaY = (mouseEvent.clientY - centerY) * strength;

    gsap.to(el, {
      x: deltaX,
      y: deltaY,
      duration: speed,
      ease: ANIMATION_PRESETS.ease.power2
    });
  };

  const handleMouseLeave = () => {
    gsap.to(el, {
      x: 0,
      y: 0,
      duration: speed * 2,
      ease: ANIMATION_PRESETS.ease.back
    });
  };

  (el as HTMLElement).addEventListener('mousemove', handleMouseMove);
  (el as HTMLElement).addEventListener('mouseleave', handleMouseLeave);

  return () => {
    (el as HTMLElement).removeEventListener('mousemove', handleMouseMove);
    (el as HTMLElement).removeEventListener('mouseleave', handleMouseLeave);
  };
};

/**
 * Scroll-triggered reveal animation
 */
export const scrollReveal = (
  element: string | Element | Element[],
  options: {
    trigger?: string | Element;
    start?: string;
    end?: string;
    scrub?: boolean | number;
    pin?: boolean;
    animation?: gsap.core.Timeline;
  } = {}
) => {
  const {
    trigger,
    start = 'top 80%',
    end = 'bottom 20%',
    scrub = false,
    pin = false,
    animation
  } = options;

  const defaultAnimation = fadeIn(element, { direction: 'up' });

  return ScrollTrigger.create({
    trigger: trigger || element,
    start,
    end,
    scrub,
    pin,
    animation: animation || defaultAnimation,
    toggleActions: 'play none none reverse'
  });
};

/**
 * Advanced page transition
 */
export const pageTransition = (
  options: {
    type?: 'slide' | 'fade' | 'scale' | 'curtain';
    direction?: 'left' | 'right' | 'up' | 'down';
    duration?: number;
    ease?: string;
  } = {}
) => {
  const {
    type = 'slide',
    direction = 'right',
    duration = ANIMATION_PRESETS.duration.slow,
    ease = ANIMATION_PRESETS.ease.power2
  } = options;

  const tl = gsap.timeline();

  switch (type) {
    case 'slide':
      const slideX = direction === 'left' ? '-100%' : direction === 'right' ? '100%' : '0%';
      const slideY = direction === 'up' ? '-100%' : direction === 'down' ? '100%' : '0%';
      
      tl.to('.page-transition', {
        x: slideX,
        y: slideY,
        duration,
        ease
      });
      break;
      
    case 'fade':
      tl.to('.page-transition', {
        opacity: 0,
        duration,
        ease
      });
      break;
      
    case 'scale':
      tl.to('.page-transition', {
        scale: 0,
        duration,
        ease
      });
      break;
      
    case 'curtain':
      tl.to('.page-transition', {
        scaleY: 0,
        transformOrigin: 'top',
        duration,
        ease
      });
      break;
  }

  return tl;
};

// ===== UTILITY FUNCTIONS =====

/**
 * Kill all GSAP animations on an element
 */
export const killAnimations = (element: string | Element) => {
  gsap.killTweensOf(element);
};

/**
 * Refresh ScrollTrigger (useful after DOM changes)
 */
export const refreshScrollTrigger = () => {
  ScrollTrigger.refresh();
};

/**
 * Check if user prefers reduced motion
 */
export const prefersReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Conditional animation based on reduced motion preference
 */
export const conditionalAnimation = (
  _element: string | Element | Element[],
  animation: () => gsap.core.Timeline | gsap.core.Tween,
  fallback?: () => void
) => {
  if (prefersReducedMotion()) {
    if (fallback) fallback();
    return null;
  }
  return animation();
};
